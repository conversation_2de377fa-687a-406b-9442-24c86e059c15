<?php

namespace App\Services\pattern;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
//Photonic Class
use App\Services\CommonService;
use App\Services\DBtool\DBTextConnecter;

class BonusSettingHelper
{
    /*-----------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------*/
    static public $table_bonus_model = 'bonus_model';

    /**
     * 取得模組資料
     */
    static public function get_bonus_models(array $params = [], bool $id_as_key = false)
    {
        $db_data = DB::connection('main_db')->table(self::$table_bonus_model);

        if (isset($params['id'])) {
            $db_data->where('id', $params['id']);
        }

        $db_data = $db_data->orderBy('id', 'desc')->get();
        $db_data = CommonService::objectToArray($db_data);

        if ($id_as_key) {
            $temp_data = [];
            foreach ($db_data as $value) {
                $temp_data[$value['id']] = $value;
            }
            $db_data = $temp_data;
        }
        return ['db_data' => $db_data];
    }

    /*取得商品類型資料*/
    static public function get_product_cate(array $params = [], bool $id_as_key = false)
    {
        $db_data = [
            ['id' => 1, 'name' => '投資'],
            ['id' => 2, 'name' => '消費'],
        ];

        if (isset($params['id'])) {
            $db_data = array_filter($db_data, function ($item) use ($params) {
                return $params['id'] == $item['id'];
            });
        }

        if ($id_as_key) {
            $temp_data = [];
            foreach ($db_data as $value) {
                $temp_data[$value['id']] = $value;
            }
            $db_data = $temp_data;
        }
        return ['db_data' => $db_data];
    }

    /*取得套用廣告資料*/
    static public function get_use_ad(array $params = [], bool $id_as_key = false)
    {
        $db_data = [
            ['id' => 0, 'name' => '否'],
            ['id' => 1, 'name' => '是'],
        ];

        if (isset($params['id'])) {
            $db_data = array_filter($db_data, function ($item) use ($params) {
                return $params['id'] == $item['id'];
            });
        }

        if ($id_as_key) {
            $temp_data = [];
            foreach ($db_data as $value) {
                $temp_data[$value['id']] = $value;
            }
            $db_data = $temp_data;
        }
        return ['db_data' => $db_data];
    }

    /*儲存資料(根據傳入資料的id==0與否判斷為新增或編輯)*/
    static public function save_bonus_model($detail_data)
    {
        if (!isset($detail_data['id'])) {
            throw new \Exception(Lang::get('資料不完整'));
        }

        if (isset($detail_data['name'])) {
            if ($detail_data['name'] == '') {
                throw new \Exception('請輸入名稱');
            }
        } else {
            throw new \Exception('請輸入名稱');
        }

        $normal_sum = 0;
        $normal_sum += (float)($detail_data['normal_recommend'] = $detail_data['normal_recommend'] ?? 0);
        $normal_sum += (float)($detail_data['normal_partner'] = $detail_data['normal_partner'] ?? 0);
        $normal_sum += (float)($detail_data['normal_marketing_dept'] = $detail_data['normal_marketing_dept'] ?? 0);
        $normal_sum += (float)($detail_data['normal_sales_dept'] = $detail_data['normal_sales_dept'] ?? 0);
        $normal_sum += (float)($detail_data['normal_executive_director'] = $detail_data['normal_executive_director'] ?? 0);
        $normal_sum += (float)($detail_data['normal_center_director'] = $detail_data['normal_center_director'] ?? 0);
        $normal_sum += (float)($detail_data['normal_center_founder'] = $detail_data['normal_center_founder'] ?? 0);
        $normal_sum += (float)($detail_data['normal_lecturer'] = $detail_data['normal_lecturer'] ?? 0);
        $normal_sum += (float)($detail_data['normal_center'] = $detail_data['normal_center'] ?? 0);
        $normal_sum += (float)($detail_data['normal_dividend_month'] = $detail_data['normal_dividend_month'] ?? 0);
        $detail_data['normal_center_divided_to_raiser'] = $detail_data['normal_center_divided_to_raiser'] ?? 0;
        if ($detail_data['normal_recommend'] < 0 || $detail_data['normal_recommend'] > 100) {
            throw new \Exception('%數應介於0~100');
        }
        if ($detail_data['normal_partner'] < 0 || $detail_data['normal_partner'] > 100) {
            throw new \Exception('%數應介於0~100');
        }
        if ($detail_data['normal_marketing_dept'] < 0 || $detail_data['normal_marketing_dept'] > 100) {
            throw new \Exception('%數應介於0~100');
        }
        if ($detail_data['normal_sales_dept'] < 0 || $detail_data['normal_sales_dept'] > 100) {
            throw new \Exception('%數應介於0~100');
        }
        if ($detail_data['normal_executive_director'] < 0 || $detail_data['normal_executive_director'] > 100) {
            throw new \Exception('%數應介於0~100');
        }
        if ($detail_data['normal_center_director'] < 0 || $detail_data['normal_center_director'] > 100) {
            throw new \Exception('%數應介於0~100');
        }
        if ($detail_data['normal_center_founder'] < 0 || $detail_data['normal_center_founder'] > 100) {
            throw new \Exception('%數應介於0~100');
        }
        if ($detail_data['normal_lecturer'] < 0 || $detail_data['normal_lecturer'] > 100) {
            throw new \Exception('%數應介於0~100');
        }
        if ($detail_data['normal_center'] < 0 || $detail_data['normal_center'] > 100) {
            throw new \Exception('%數應介於0~100');
        }
        if ($detail_data['normal_dividend_month'] < 0 || $detail_data['normal_dividend_month'] > 100) {
            throw new \Exception('%數應介於0~100');
        }
        if ($detail_data['normal_center_divided_to_raiser'] < 0 || $detail_data['normal_center_divided_to_raiser'] > 100) {
            throw new \Exception('%數應介於0~100');
        }
        if ($normal_sum != 100) {
            throw new \Exception('一般回饋設定合計應為100%');
        }

        if (($detail_data['use_partner_mode'] ?? 0) == 1) {
            $partner_sum = 0;
            $partner_sum += (float)($detail_data['partner_recommend'] = $detail_data['partner_recommend'] ?? 0);
            $partner_sum += (float)($detail_data['partner_partner'] = $detail_data['partner_partner'] ?? 0);
            $partner_sum += (float)($detail_data['partner_marketing_dept'] = $detail_data['partner_marketing_dept'] ?? 0);
            $partner_sum += (float)($detail_data['partner_sales_dept'] = $detail_data['partner_sales_dept'] ?? 0);
            $partner_sum += (float)($detail_data['partner_executive_director'] = $detail_data['partner_executive_director'] ?? 0);
            $partner_sum += (float)($detail_data['partner_center_director'] = $detail_data['partner_center_director'] ?? 0);
            $partner_sum += (float)($detail_data['partner_center_founder'] = $detail_data['partner_center_founder'] ?? 0);
            $partner_sum += (float)($detail_data['partner_lecturer'] = $detail_data['partner_lecturer'] ?? 0);
            $partner_sum += (float)($detail_data['partner_center'] = $detail_data['partner_center'] ?? 0);
            $partner_sum += (float)($detail_data['partner_dividend_month'] = $detail_data['partner_dividend_month'] ?? 0);
            $detail_data['partner_center_divided_to_raiser'] = $detail_data['partner_center_divided_to_raiser'] ?? 0;
            if ($detail_data['partner_recommend'] < 0 || $detail_data['partner_recommend'] > 100) {
                throw new \Exception('%數應介於0~100');
            }
            if ($detail_data['partner_partner'] < 0 || $detail_data['partner_partner'] > 100) {
                throw new \Exception('%數應介於0~100');
            }
            if ($detail_data['partner_marketing_dept'] < 0 || $detail_data['partner_marketing_dept'] > 100) {
                throw new \Exception('%數應介於0~100');
            }
            if ($detail_data['partner_sales_dept'] < 0 || $detail_data['partner_sales_dept'] > 100) {
                throw new \Exception('%數應介於0~100');
            }
            if ($detail_data['partner_executive_director'] < 0 || $detail_data['partner_executive_director'] > 100) {
                throw new \Exception('%數應介於0~100');
            }
            if ($detail_data['partner_center_director'] < 0 || $detail_data['partner_center_director'] > 100) {
                throw new \Exception('%數應介於0~100');
            }
            if ($detail_data['partner_center_founder'] < 0 || $detail_data['partner_center_founder'] > 100) {
                throw new \Exception('%數應介於0~100');
            }
            if ($detail_data['partner_lecturer'] < 0 || $detail_data['partner_lecturer'] > 100) {
                throw new \Exception('%數應介於0~100');
            }
            if ($detail_data['partner_center'] < 0 || $detail_data['partner_center'] > 100) {
                throw new \Exception('%數應介於0~100');
            }
            if ($detail_data['partner_dividend_month'] < 0 || $detail_data['partner_dividend_month'] > 100) {
                throw new \Exception('%數應介於0~100');
            }
            if ($detail_data['partner_center_divided_to_raiser'] < 0 || $detail_data['partner_center_divided_to_raiser'] > 100) {
                throw new \Exception('%數應介於0~100');
            }
            if ($partner_sum != 100) {
                throw new \Exception('合夥批發回饋設定合計應為100%');
            }
        } else {
            $detail_data['partner_recommend'] = 0;
            $detail_data['partner_partner'] = 0;
            $detail_data['partner_marketing_dept'] = 0;
            $detail_data['partner_sales_dept'] = 0;
            $detail_data['partner_executive_director'] = 0;
            $detail_data['partner_center_director'] = 0;
            $detail_data['partner_center_founder'] = 0;
            $detail_data['partner_lecturer'] = 0;
            $detail_data['partner_center'] = 0;
            $detail_data['partner_center_divided_to_raiser'] = 0;
            $detail_data['partner_dividend_month'] = 0;
        }

        if (isset($detail_data['ad_bonus'])) {
            if ($detail_data['ad_bonus'] < 0 || $detail_data['ad_bonus'] > 100) {
                throw new \Exception('%數應介於0~100');
            }
        } else {
            $detail_data['ad_bonus'] = 0;
        }

        // dd($detail_data);
        $DBTextConnecter = DBTextConnecter::withTableName(self::$table_bonus_model, 'main_db');
        if ($detail_data['id'] == 0) { /*新增*/
            unset($detail_data['id']);
            $DBTextConnecter->setDataArray($detail_data);
            $id = $DBTextConnecter->createTextRow();
        } else { /*編輯*/
            $DBTextConnecter->setDataArray($detail_data);
            $DBTextConnecter->upTextRow();
            $id = $detail_data['id'];
        }
        return $id;
    }
    /*刪除資料*/
    static public function delete_bonus_model($item_id)
    {
        if (!$item_id) {
            throw new \Exception(Lang::get('資料不完整'));
        }

        $db_data = DB::connection('main_db')->table(self::$table_bonus_model);
        $db_data = $db_data->where('id', $item_id);
        return $db_data->delete();
    }

    /*取得回饋設定資料*/
    static public function get_bonus_setting()
    {
        $db_data = DB::connection('main_db')->table('bonus_setting')->orderBy('id', 'asc')->get();
        $db_data = CommonService::objectToArray($db_data);

        $bonus_setting = [
            'pi_value' => (float)$db_data[0]['value'], //增值積分現值(會在「處理回饋」後，會根據『「積分資金池」總額 / 目前增值積分總數』算出，勿以其他方式任意修改。)
            'cv_rate' => (float)($db_data[1]['value'] / 100), //CV金額分潤比率(使用時要除100)
            'ad_partner_rate' => (float)($db_data[2]['value'] / 100), //廣告合夥分潤比率(使用時要除100)
            'limit_c_rate' => (float)($db_data[3]['value']), //消費圓滿點數倍率
            'limit_o_rate' => (float)($db_data[4]['value']), //其他圓滿點數倍率
            'divided_times' => json_decode($db_data[5]['value'], true) ?? [50, 30, 20], //月分紅期數(json格式)(使用時要除100)(空時不進行月分紅)
            'recommend_times' => json_decode($db_data[6]['value'], true) ?? [10, 20, 70], //推三反本第幾個會員比率(json格式)(使用時要除100)(空時不進行推三反本)
            'charge_tax' => (float)($db_data[7]['value'] / 100), //提現-代扣稅費(使用時要除100)
            'charge_pool' => (float)($db_data[8]['value'] / 100), //提現-轉入資金池(使用時要除100)
            'month_point2cash' => (int)($db_data[9]['value']), //定時處理-增值轉現金月數
            'month_limit2zero' => (int)($db_data[10]['value']), //定時處理-圓滿點清零月數
            'month_weight_partner' => (float)($db_data[11]['value']), //月分紅-新推廣合伙人人數加權
            'month_weight_gv_num' => (int)($db_data[12]['value']), //月分紅-個人點數達標金額
            'month_weight_gv' => (float)($db_data[13]['value']), //月分紅-個人點數達標金額次數加權
            'member_transfer_point_increasable' => (float)($db_data[14]['value']), //前台開放會員轉移「增值積分」(1.允許 0.不允許)
            'member_point_to_cash' => (float)($db_data[15]['value']), //會員「現金積分」提現(1.允許 0.不允許)
            'cv_distribution_push_ratio' => (float)($db_data[17]['value'] ?? 85), //推三反一分配比例(預設85%)
            'cv_distribution_marketing_dept_ratio' => (float)($db_data[18]['value'] ?? 5), //推三反一行政部門分配比例(預設5%)
            'cv_distribution_sales_dept_ratio' => (float)($db_data[19]['value'] ?? 5), //推三反一業務部門分配比例(預設5%)
            'cv_distribution_center_ratio' => (float)($db_data[20]['value'] ?? 5), //中心分配比例(預設5%)
        ];
        return $bonus_setting;
    }
}
